[package]
edition = "2021"
name = "embassy-stm32f4-examples"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[dependencies]
embassy-stm32 = { version = "0.2.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "stm32f407ve",
    "unstable-pac",
    "memory-x",
    "time-driver-tim4",
    "exti",
    "chrono",
] }
embassy-sync = { branch = "main", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-executor = { branch = "main", git = "https://github.com/embassy-rs/embassy.git", features = [
    "arch-cortex-m",
    "executor-thread",
    "executor-interrupt",
    "defmt",
] }
embassy-time = { version = "0.4.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768",
] }
embassy-usb = { version = "0.5.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-net = { version = "0.7.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
    "tcp",
    "dhcpv4",
    "medium-ethernet",
] }
embassy-net-wiznet = { version = "0.2.0", git = "https://github.com/embassy-rs/embassy.git", features = [
    "defmt",
] }
embassy-futures = { version = "0.1.0", git = "https://github.com/embassy-rs/embassy.git" }

defmt = "1.0.1"
defmt-rtt = "1.0.0"

cortex-m = { version = "0.7.6", features = [
    "inline-asm",
    "critical-section-single-core",
] }
cortex-m-rt = "0.7.0"
embedded-hal = "1.0.0"

embedded-io = { version = "0.6.0" }
embedded-io-async = { version = "0.6.1" }
panic-probe = { version = "1.0.0", features = ["print-defmt"] }
futures-util = { version = "0.3.30", default-features = false }
heapless = { version = "0.8", default-features = false }
critical-section = "1.1"
nb = "1.1.0"
embedded-storage = "0.3.1"
micromath = "2.0.0"
usbd-hid = "0.8.1"
static_cell = "2"
chrono = { version = "^0.4", default-features = false }


# ==================== local ==================== 
drv8833 = { path = "D:/Code/Rust/embeded-rs/library/drv8833" }
# embedded-graphics = "0.8.1"
# pid = "4.0.0"
# hcsr04 = { path = "D:/Code/Rust/embeded-rs/library/hcsr04" }
# mpu6500 = { git = "https://github.com/Alddp/mpu6500-rs.git" }


[[bin]]
name = "embassy-stm32f4-examples"
path = "src/main.rs"
bench = false
test = false

[profile.release]
opt-level = "s"
debug = true
codegen-units = 1
overflow-checks = false
lto = true
incremental = false
