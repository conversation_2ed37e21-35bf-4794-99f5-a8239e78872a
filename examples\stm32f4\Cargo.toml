[package]
edition = "2021"
name = "embassy-stm32f4-examples"
version = "0.1.0"
license = "MIT OR Apache-2.0"

[dependencies]
embassy-stm32 = { git = "https://github.com/embassy-rs/embassy.git", branch = "main", features = [
    "defmt",
    "stm32f407ve", # Assuming this is the correct chip feature for you
    "unstable-pac", # Usually needed when pulling from git
    "memory-x",     # For memory layout in .cargo/config.toml
    "time-driver-tim4", # Keep if using TIM4 as the embassy-time driver
    "exti",         # External interrupts for GPIO
    
    
    
    # "chrono",     # Remove if you don't need it or enable it conditionally
    # "gpio",       # <-- REMOVED: GPIO is usually implicitly enabled by chip feature
] }
embassy-executor = { git = "https://github.com/embassy-rs/embassy.git", branch = "main", features = [
    "arch-cortex-m",
    "executor-thread",
    # "executor-interrupt", # Uncomment if you want interrupt-based executor
    "defmt",
] }
embassy-time = { git = "https://github.com/embassy-rs/embassy.git", branch = "main", features = [
    "defmt",
    "defmt-timestamp-uptime",
    "tick-hz-32_768", # Keep this for your time base
] }
embassy-futures = { git = "https://github.com/embassy-rs/embassy.git", branch = "main" }

# defmt is already pulled by embassy crates with the "defmt" feature
# defmt = "1.0.1" # <-- Ensure this line is still REMOVED
defmt-rtt = "1.0.0" # Keep this

cortex-m = { version = "0.7.6", features = [
    "inline-asm",
    "critical-section-single-core",
] }
cortex-m-rt = "0.7.0"
embedded-hal = "1.0.0"


panic-probe = { version = "1.0.0", features = ["print-defmt"] }
futures-util = { version = "0.3.30", default-features = false }
heapless = { version = "0.8", default-features = false }
critical-section = "1.1"
nb = "1.1.0"
embedded-storage = "0.3.1"
micromath = "2.0.0"
static_cell = "2"
chrono = { version = "^0.4", default-features = false, optional = true }




[[bin]]
name = "embassy-stm32f4-examples"
bench = false
test = false

[lib]
test = false
bench = false

[profile.release]
opt-level = "s"
debug = true
codegen-units = 1
overflow-checks = false
lto = true
incremental = false