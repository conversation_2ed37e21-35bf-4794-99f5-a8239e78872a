#![no_std]
#![no_main]

use defmt::*;
use drv8833::at8236::At8236Single;
use drv8833::mecanum::{GenericMecanumDrive, MecanumDirection, MecanumDrive};
use drv8833::Drv8833Single;
use embassy_executor::Spawner;
use embassy_stm32::gpio::{Level, Output, Speed};
use embassy_stm32::time::Hertz;
use embassy_stm32::timer::simple_pwm::{PwmPin, SimplePwm};
use embassy_stm32::Config;
use embassy_time::{Duration, Timer};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let mut config = Config::default();
    config.rcc.sys_ck = Some(rcc::Hertz(168_000_000)); // F407 usually runs up to 168MHz
    config.rcc.apb1_tim_ck = Some(rcc::Hertz(84_000_000)); // APB1 timers can be 2x APB1_CLK if APB1_PSC > 1

    let p = embassy_stm32::init(config);

    // 初始化四个电动机的PWM通道
    use embassy_stm32::peripherals::*;
    use embassy_stm32::timer::{Ch1 as Ch1_9, Ch2 as Ch2_9};
    use embassy_stm32::timer::{Ch1, Ch2, Ch3, Ch4};

    // 这里只用TIM3的四个通道和对应的引脚（假设你的芯片支持这些映射）
    let fl_pwm_pin1 = PwmPin::<TIM1, Ch1>::new(p.PE9, embassy_stm32::gpio::OutputType::PushPull);
    let fl_pwm_pin2 = PwmPin::<TIM1, Ch2>::new(p.PE11, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pwm_pin1 = PwmPin::<TIM1, Ch3>::new(p.PE13, embassy_stm32::gpio::OutputType::PushPull);
    let fr_pwm_pin2 = PwmPin::<TIM1, Ch4>::new(p.PE14, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin1 = PwmPin::<TIM9, Ch1_9>::new(p.PE5, embassy_stm32::gpio::OutputType::PushPull);
    let bl_pwm_pin2 = PwmPin::<TIM9, Ch2_9>::new(p.PE6, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin1 = PwmPin::<TIM10, Ch1>::new(p.PB8, embassy_stm32::gpio::OutputType::PushPull);
    let br_pwm_pin2 = PwmPin::<TIM11, Ch1>::new(p.PB9, embassy_stm32::gpio::OutputType::PushPull);

    let pwm_tim3 = SimplePwm::new(
        p.TIM1,
        Some(fl_pwm_pin1),
        Some(fl_pwm_pin2),
        Some(fr_pwm_pin1),
        Some(fr_pwm_pin2),
        Hertz(10_000),
        Default::default(),
    );

    let pwm_tim9 = SimplePwm::new(
        p.TIM9,
        Some(bl_pwm_pin1),
        Some(bl_pwm_pin2),
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );

    let pwm_tim10 = SimplePwm::new(
        p.TIM10,
        Some(br_pwm_pin1),
        None,
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );

    let pwm_tim11 = SimplePwm::new(
        p.TIM11,
        Some(br_pwm_pin2),
        None,
        None,
        None,
        Hertz(10_000),
        Default::default(),
    );

    let tim3_chs = pwm_tim3.split();
    let tim9_chs = pwm_tim9.split();
    let tim10_chs = pwm_tim10.split();
    let tim11_chs = pwm_tim11.split();

    // 获取PWM频道
    let mut fl_pwm_in1 = tim3_chs.ch1;
    let mut fl_pwm_in2 = tim3_chs.ch2;
    let mut fr_pwm_in1 = tim3_chs.ch3;
    let mut fr_pwm_in2 = tim3_chs.ch4;

    let mut bl_pwm_in1 = tim9_chs.ch1;
    let mut bl_pwm_in2 = tim9_chs.ch2;
    let mut br_pwm_in1 = tim10_chs.ch1;
    let mut br_pwm_in2 = tim11_chs.ch1;

    fl_pwm_in1.enable();
    fl_pwm_in2.enable();
    fr_pwm_in1.enable();
    fr_pwm_in2.enable();
    bl_pwm_in1.enable();
    bl_pwm_in2.enable();
    br_pwm_in1.enable();
    br_pwm_in2.enable();

    // 创建麦克纳姆底盘驱动
    let mut mecanum = GenericMecanumDrive::new(
        At8236Single::new(fl_pwm_in1, fl_pwm_in2), // 左前
        At8236Single::new(fr_pwm_in1, fr_pwm_in2), // 右前
        At8236Single::new(bl_pwm_in1, bl_pwm_in2), // 左后
        At8236Single::new(br_pwm_in1, br_pwm_in2), // 左后
    );

    info!("✅ AT8236 麦克纳姆驱动系统初始化完成");

    loop {
        info!("🔄 AT8236 前进");
        let _ = mecanum.move_direction(MecanumDirection::Forward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 后退");
        let _ = mecanum.move_direction(MecanumDirection::Backward, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 左平移");
        let _ = mecanum.move_direction(MecanumDirection::Left, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("🔄 AT8236 右平移");
        let _ = mecanum.move_direction(MecanumDirection::Right, 50);
        Timer::after(Duration::from_secs(2)).await;

        info!("⏸️ AT8236 停止");
        let _ = mecanum.stop_all();
        Timer::after(Duration::from_secs(1)).await;
    }
}
